#!/usr/bin/env python3
"""
测试策略参数预设系统
"""

from optimization_engine import OptimizationEngine
from strategies import ETHPriceReversalStrategy, get_available_strategies

def test_strategy_presets():
    """测试策略类的预设参数"""
    print("=== 测试策略类的预设参数 ===")
    
    # 测试ETH策略的预设参数
    eth_presets = ETHPriceReversalStrategy.get_parameter_presets()
    
    print("ETH价格反转策略的预设参数:")
    for preset_key, preset_data in eth_presets.items():
        print(f"\n{preset_key} ({preset_data['name']}):")
        print(f"  描述: {preset_data['description']}")
        print(f"  参数:")
        for param_name, param_config in preset_data['parameters'].items():
            if 'enabled' in param_config and not param_config['enabled']:
                print(f"    {param_name}: 固定值 = {param_config.get('fixed_value', 'N/A')}")
            elif 'fixed_value' in param_config:
                print(f"    {param_name}: 固定值 = {param_config['fixed_value']}")
            else:
                print(f"    {param_name}: min={param_config.get('min', 'N/A')}, "
                      f"max={param_config.get('max', 'N/A')}, step={param_config.get('step', 'N/A')}")
    
    return eth_presets

def test_optimization_engine_presets():
    """测试优化引擎的预设参数获取"""
    print("\n=== 测试优化引擎的预设参数获取 ===")
    
    engine = OptimizationEngine()
    
    # 测试ETH策略
    print("通过优化引擎获取ETH策略预设:")
    result = engine.get_parameter_presets('eth_price_reversal')
    
    if 'error' in result:
        print(f"❌ 错误: {result['error']}")
    else:
        print(f"✅ 成功获取策略 {result['strategy_id']} 的预设参数")
        print(f"预设数量: {len(result['presets'])}")
        
        for preset_key, preset_data in result['presets'].items():
            print(f"\n{preset_key}:")
            print(f"  描述: {preset_data['description']}")
            print(f"  参数数量: {len(preset_data['ranges'])}")
            
            # 显示前几个参数
            for i, (param_name, param_config) in enumerate(preset_data['ranges'].items()):
                if i < 3:  # 只显示前3个参数
                    print(f"    {param_name}: {param_config}")
    
    # 测试不存在的策略
    print("\n测试不存在的策略:")
    result_invalid = engine.get_parameter_presets('non_existent_strategy')
    if 'error' in result_invalid:
        print(f"✅ 正确处理不存在的策略: {result_invalid['error']}")
    else:
        print(f"❌ 应该返回错误，但返回了: {result_invalid}")
    
    # 测试传统策略（使用硬编码预设）
    print("\n测试传统策略（simple_rsi）:")
    result_legacy = engine.get_parameter_presets('simple_rsi')
    if 'error' in result_legacy:
        print(f"❌ 错误: {result_legacy['error']}")
    else:
        print(f"✅ 成功获取传统策略预设，预设数量: {len(result_legacy['presets'])}")
    
    return result

def test_available_strategies():
    """测试可用策略列表"""
    print("\n=== 测试可用策略列表 ===")
    
    strategies = get_available_strategies()
    
    print(f"可用策略数量: {len(strategies)}")
    
    for strategy in strategies:
        print(f"\n策略: {strategy['name']} (ID: {strategy['id']})")
        
        # 检查是否有get_parameter_presets方法
        if hasattr(strategy['class'], 'get_parameter_presets'):
            print(f"  ✅ 支持动态预设参数")
            try:
                presets = strategy['class'].get_parameter_presets()
                print(f"  预设数量: {len(presets)}")
            except Exception as e:
                print(f"  ❌ 获取预设参数时出错: {e}")
        else:
            print(f"  ⚠️  使用硬编码预设参数")

def test_preset_application():
    """测试预设参数的应用"""
    print("\n=== 测试预设参数的应用 ===")
    
    # 获取ETH策略的保守型预设
    eth_presets = ETHPriceReversalStrategy.get_parameter_presets()
    conservative_preset = eth_presets['conservative']
    
    print("保守型预设参数:")
    print(f"描述: {conservative_preset['description']}")
    
    # 模拟前端应用预设的过程
    print("\n模拟前端应用预设:")
    applied_config = {}
    
    for param_name, param_config in conservative_preset['parameters'].items():
        if 'enabled' in param_config and not param_config['enabled']:
            # 固定参数
            applied_config[param_name] = {
                'enabled': False,
                'fixed_value': param_config.get('fixed_value')
            }
            print(f"  {param_name}: 固定为 {param_config.get('fixed_value')}")
        elif 'fixed_value' in param_config:
            # 有固定值的参数
            applied_config[param_name] = {
                'enabled': False,
                'fixed_value': param_config['fixed_value']
            }
            print(f"  {param_name}: 固定为 {param_config['fixed_value']}")
        else:
            # 优化参数
            applied_config[param_name] = {
                'enabled': True,
                'min': param_config.get('min'),
                'max': param_config.get('max'),
                'step': param_config.get('step')
            }
            print(f"  {param_name}: 优化范围 {param_config.get('min')}-{param_config.get('max')}, 步长{param_config.get('step')}")
    
    print(f"\n应用后的配置:")
    print(f"启用优化的参数: {[k for k, v in applied_config.items() if v['enabled']]}")
    print(f"固定参数: {[k for k, v in applied_config.items() if not v['enabled']]}")
    
    return applied_config

if __name__ == "__main__":
    try:
        # 测试策略类的预设参数
        eth_presets = test_strategy_presets()
        
        # 测试优化引擎的预设参数获取
        engine_result = test_optimization_engine_presets()
        
        # 测试可用策略列表
        test_available_strategies()
        
        # 测试预设参数的应用
        applied_config = test_preset_application()
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
