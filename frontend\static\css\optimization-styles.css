/* 策略参数优化页面专用样式 */

/* 收藏按钮样式优化 */
.favorite-btn {
    border: 1px solid var(--border-color) !important;
    border-left: none !important;
    border-radius: 0 var(--border-radius-base) var(--border-radius-base) 0 !important;
    background-color: var(--input-bg-color);
    color: var(--text-secondary-color);
    transition: all 0.2s ease;
}

.favorite-btn:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color) !important;
    color: var(--text-primary-color);
}

.favorite-btn .bi-star-fill {
    color: var(--warning-color);
}

/* 结果卡片样式 */
.result-card {
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    border-left-width: 4px;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.result-card .card-header {
    background-color: var(--table-header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
}

.result-card .card-body {
    padding: 1rem;
    font-size: 0.85rem;
}

/* 使用与其他页面一致的 trade-detail-item 样式 */
.trade-detail-item {
    background-color: var(--input-bg-color);
    border-radius: var(--border-radius-base);
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
}

.trade-detail-label {
    font-size: 0.75em;
    color: var(--text-secondary-color);
    margin-bottom: 0.15rem;
}

.trade-detail-value {
    font-size: 1em;
    font-weight: 600;
    line-height: 1.2;
}

/* 参数范围配置区域样式优化 */
.parameter-range-container {
    background-color: var(--input-bg-color);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius-base);
    padding: 1rem;
    transition: all 0.3s ease;
}

.parameter-range-container:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-color-light);
}

/* 进度条样式优化 */
.progress {
    height: 8px;
    border-radius: var(--border-radius-base);
    background-color: var(--border-color);
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary-color);
    border-radius: var(--border-radius-base);
    transition: width 0.6s ease;
}

/* 散点图容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
    background: var(--card-bg-color);
    border-radius: var(--border-radius-base);
    padding: 1rem;
    border: 1px solid var(--border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
        padding: 0.5rem;
    }

    .result-card .metric-value {
        font-size: 0.8rem;
    }

    .result-card .metric-label {
        font-size: 0.7rem;
    }
}

/* 参数显示样式 */
.parameter-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    padding: 0.5rem 0.75rem;
    text-align: center;
    transition: all 0.2s ease;
    min-width: 80px;
}

/* 投资策略配置样式 */
.investment-strategy-container {
    background-color: var(--input-bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    transition: all 0.3s ease;
}

.investment-strategy-container:hover {
    border-color: var(--success-color);
    background-color: var(--success-color-light);
}

.investment-strategy-params {
    background-color: rgba(var(--success-color-rgb), 0.05);
    border: 1px dashed var(--success-color);
    border-radius: var(--border-radius-base);
}

/* 投资策略参数输入框样式 */
.investment-strategy-params .form-control {
    border-color: var(--success-color);
    background-color: rgba(255, 255, 255, 0.9);
}

.investment-strategy-params .form-control:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--success-color-rgb), 0.25);
}

.parameter-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.parameter-label {
    font-size: 0.7rem;
    color: var(--text-secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.parameter-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* 星期选择器样式 */
.weekday-selector {
    display: flex;
    justify-content: space-between;
    gap: 6px;
    margin-top: 8px;
}

.weekday-btn {
    flex-grow: 1;
    padding: 6px 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    -webkit-user-select: none; /* Safari */
    user-select: none;
}

.weekday-btn:hover {
    background-color: #f0f2f5;
    border-color: #adb5bd;
}

.weekday-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 最佳结果卡片特殊样式 */
.best-result-card {
    border-left-width: 6px !important;
    border-left-color: #f39c12 !important;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.2) !important;
    position: relative;
    overflow: hidden;
}

.best-result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #f39c12 0%, #e67e22 50%, #f39c12 100%);
    z-index: 1;
}

.best-result-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(243, 156, 18, 0.3) !important;
}

.best-result-card .card-header {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border-bottom: 2px solid #f39c12;
    position: relative;
    z-index: 2;
}

.best-result-card .badge.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
    color: white !important;
    font-weight: 700;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
}

.best-result-card .badge.bg-warning::after {
    content: '👑';
    margin-left: 0.25rem;
    font-size: 0.8em;
}

/* 最佳结果卡片的按钮样式优化 */
.best-result-card .btn-outline-secondary {
    border-color: #f39c12;
    color: #f39c12;
    background-color: rgba(255, 255, 255, 0.9);
}

.best-result-card .btn-outline-secondary:hover {
    background-color: #f39c12;
    border-color: #f39c12;
    color: white;
}

.best-result-card .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.best-result-card .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.4);
}

/* 结果排序控件优化 */
.results-sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sort-label {
    font-size: 0.8rem;
    color: var(--text-secondary-color);
    font-weight: 500;
    white-space: nowrap;
}

.sort-controls-group {
    display: flex;
    align-items: center;
    gap: 4px;
}

.sort-select {
    min-width: 120px;
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    font-size: 0.8rem;
    padding: 4px 8px;
    background-color: white;
    transition: all 0.2s ease;
}

.sort-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.15rem rgba(var(--primary-color-rgb), 0.25);
}

.sort-direction-btn {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: white;
    border-radius: 6px;
    padding: 4px 8px;
    transition: all 0.2s ease;
    min-width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sort-direction-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-direction-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .results-sort-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
        padding: 6px 8px;
    }

    .sort-label {
        text-align: center;
        font-size: 0.75rem;
    }

    .sort-controls-group {
        justify-content: center;
    }

    .best-result-card .badge.bg-warning {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}


