<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast通知测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Toast通知测试</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">点击下面的按钮测试不同类型的Toast通知：</p>
                        
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="testSuccessToast()">
                                <i class="bi bi-check-circle me-2"></i>成功通知
                            </button>
                            
                            <button type="button" class="btn btn-danger" onclick="testErrorToast()">
                                <i class="bi bi-x-octagon me-2"></i>错误通知
                            </button>
                            
                            <button type="button" class="btn btn-warning" onclick="testWarningToast()">
                                <i class="bi bi-exclamation-triangle me-2"></i>警告通知
                            </button>
                            
                            <button type="button" class="btn btn-info" onclick="testInfoToast()">
                                <i class="bi bi-info-circle me-2"></i>信息通知
                            </button>
                            
                            <button type="button" class="btn btn-secondary" onclick="testLongMessage()">
                                <i class="bi bi-chat-text me-2"></i>长消息测试
                            </button>
                            
                            <button type="button" class="btn btn-primary" onclick="testHtmlMessage()">
                                <i class="bi bi-code me-2"></i>HTML消息测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/toast-utils.js"></script>
    
    <script>
        function testSuccessToast() {
            showSuccessToast('操作成功', '这是一个成功的操作通知！');
        }
        
        function testErrorToast() {
            showErrorToast('操作失败', '这是一个错误通知，请检查您的操作。');
        }
        
        function testWarningToast() {
            showWarningToast('注意', '这是一个警告通知，请谨慎操作。');
        }
        
        function testInfoToast() {
            showInfoToast('提示信息', '这是一个信息通知，为您提供相关信息。');
        }
        
        function testLongMessage() {
            showToast('长消息测试', '这是一个很长的消息内容，用来测试Toast通知在显示长文本时的表现。消息内容可能会包含多行文字，需要确保显示效果良好。这个测试可以帮助我们验证Toast组件在处理长文本时的布局和样式是否正确。', 'info', 8000);
        }
        
        function testHtmlMessage() {
            showToast('HTML消息测试', '这是一个包含<strong>HTML标签</strong>的消息：<br>• 第一项<br>• 第二项<br>• <em>斜体文字</em>', 'warning', 6000);
        }
    </script>
</body>
</html>
