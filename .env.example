# Binance API密钥，用于连接Binance交易平台，执行自动化交易
BINANCE_API_KEY="YOUR_BINANCE_API_KEY"

# Binance API密钥，用于授权Binance交易平台上的操作
BINANCE_API_SECRET="YOUR_BINANCE_API_SECRET"

#根据是否需要使用代理true或者false
USE_PROXY="YOUR_PROXY_SETTING"
#你的HTTP代理地址,例如clash为http://127.0.0.1:7890
PROXY_HTTP_URL="YOUR_PROXY_HTTP_URL:PORT"
#你的HTTPS代理地址,例如clash为https://127.0.0.1:7890
PROXY_HTTPS_URL="YOUR_PROXY_HTTPS_URL:PORT"

#服务器地址，单机访问设置127.0.0.1，局域网访问"0.0.0.0"
SERVER_HOST="YOUR_SERVER_HOST"
#服务器端口，任意选择一个未被占用的端口
SERVER_PORT="YOUR_SERVER_PORT"

#WebSocket重连尝试间隔
WS_RECONNECT_INTERVAL="YOUR_WS_RECONNECT_INTERVAL"
#WebSocket重连尝试次数
WS_RECONNECT_ATTEMPTS="YOUR_WS_RECONNECT_ATTEMPTS"

#日志文件夹名称
LOG_DIR="logs"
#日志文件名
LOG_FILENAME="service.log"
#日志轮转时间
LOG_ROTATION_WHEN="midnight"
#日志轮转间隔（单位:天）
LOG_ROTATION_INTERVAL="1"
#日志备份计数
LOG_BACKUP_COUNT="2"
