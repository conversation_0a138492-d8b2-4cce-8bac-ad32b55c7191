/* frontend/static/css/autox-manager-styles.css */

body {
    background-color: var(--bs-gray-100); /* Slightly off-white background for the page */
}

[v-cloak] {
    display: none;
}

/* --- Modern Table Design --- */
.modern-table {
    border-collapse: separate; /* Allows for border-spacing */
    border-spacing: 0;       /* Reset spacing, control padding via td/th */
    width: 100%;
    font-size: 0.875rem; /* Slightly smaller base font for table content */
}

.modern-table thead th {
    background-color: transparent; /* No background color for header */
    border-bottom: 2px solid var(--bs-gray-300); /* Thicker, lighter bottom border for header */
    border-top: none;
    color: var(--bs-gray-700); /* Darker gray for header text */
    font-weight: 600; /* Medium-bold for header text */
    padding: 0.9rem 1rem;  /* Increased padding for more space */
    text-align: left;
    white-space: nowrap;
    vertical-align: middle;
}

.modern-table tbody td {
    padding: 0.9rem 1rem; /* Consistent padding with header */
    border-top: 1px solid var(--bs-gray-200); /* Lighter horizontal lines for rows */
    vertical-align: middle;
    color: var(--bs-gray-800); /* Standard text color */
    line-height: 1.6;
}

/* Remove top border for the first row in tbody to avoid double border with header */
.modern-table tbody tr:first-child td {
    border-top: none;
}

/* Hover effect - subtle */
.modern-table.table-hover tbody tr:hover {
    background-color: var(--bs-gray-100); /* Very light gray hover */
}

/* Striped rows - subtle (optional, Bootstrap .table-striped class handles this) */
/* If using .table-striped, Bootstrap will handle it. If custom, uncomment and style: */
/*
.modern-table tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.02);
}
*/

/* Card integration - if card-body has p-0 */
/* .card-body.p-0 .modern-table { */
    /* If you want the table to have rounded corners matching the card */
    /* This is tricky if table is scrollable. Better to let card handle rounding. */
/* } */
.card-body.p-0 .modern-table thead th:first-child {
    padding-left: 1.25rem; 
    /* Adjust if card has its own padding you want to mimic */
}
.card-body.p-0 .modern-table thead th:last-child {
    padding-right: 1.25rem;
}
.card-body.p-0 .modern-table tbody td:first-child {
    padding-left: 1.25rem;
}
.card-body.p-0 .modern-table tbody td:last-child {
    padding-right: 1.25rem;
}


/* --- Specific Column Styling for AutoX Page --- */
.autox-clients-table .client-id-cell,
.modern-table .id-cell { /* For Client ID and Signal ID in logs */
    max-width: 160px;
    word-break: break-all;
    font-size: 0.825em; /* Slightly smaller for IDs */
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
    color: var(--bs-gray-600); /* Lighter color for IDs */
}
.autox-clients-table .client-id-cell:hover,
.modern-table .id-cell:hover {
    color: var(--bs-primary); /* Highlight on hover */
}


.notes-input {
    min-width: 180px;
    font-size: 0.875em;
    border-radius: var(--bs-border-radius-sm); /* Smaller radius for inputs inside table */
    border: 1px solid var(--bs-gray-300);
}
.notes-input:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.15); /* Softer focus shadow */
}

.supported-symbols-list .badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    font-size: 0.78em; /* Smaller badges */
}

.hsl-badge {
    /* Using Bootstrap's text-emphasis colors for better theme integration if possible */
    /* This is a fallback if direct HSL is preferred */
    background-color: hsl(var(--symbol-hue, 210), 80%, 92%) !important;
    color: hsl(var(--symbol-hue, 210), 60%, 35%) !important;
    border: 1px solid hsl(var(--symbol-hue, 210), 70%, 85%) !important;
    font-weight: 500;
}


.log-details-cell {
    max-width: 400px;
    font-size: 0.825em;
    line-height: 1.5;
}
.log-details-cell pre {
    background-color: var(--bs-gray-100);
    padding: 0.6rem 0.8rem;
    border-radius: var(--bs-border-radius-sm);
    border: 1px solid var(--bs-gray-300);
    font-size: 0.9em;
    color: var(--bs-code-color); /* Use Bootstrap's code color */
    max-height: 180px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}
.log-details-cell a {
    color: var(--bs-link-color);
}
.log-details-cell a:hover {
    color: var(--bs-link-hover-color);
}


/* Status Badges in Table */
.modern-table .badge {
    min-width: 70px;
    text-align: center;
    font-size: 0.78em; /* Consistent small badge size */
    padding: 0.4em 0.65em;
}

/* Soft background highlights for table rows (Bootstrap 5.3 variables) */
.table-danger-soft {
    --bs-table-bg: var(--bs-danger-bg-subtle);
    --bs-table-border-color: var(--bs-danger-border-subtle);
    --bs-table-striped-bg: var(--bs-danger-bg-subtle);
    color: var(--bs-danger-text-emphasis) !important;
}
.table-danger-soft .id-cell,
.table-danger-soft .log-details-cell strong {
    color: var(--bs-danger-text-emphasis) !important; /* Ensure IDs and strong text also get colored */
}

.table-success-soft {
    --bs-table-bg: var(--bs-success-bg-subtle);
    --bs-table-border-color: var(--bs-success-border-subtle);
    --bs-table-striped-bg: var(--bs-success-bg-subtle);
    color: var(--bs-success-text-emphasis) !important;
}
.table-success-soft .id-cell,
.table-success-soft .log-details-cell strong {
    color: var(--bs-success-text-emphasis) !important;
}

/* Card header adjustments */
.card-header h5 {
    font-weight: 600;
    color: var(--bs-gray-800);
}
.card-header h5 i.bi {
    font-size: 1em; /* Adjusted icon size */
    color: var(--bs-primary); /* Icon color matching primary theme */
    margin-right: 0.5rem;
}
.card-header .btn-outline-secondary {
    --bs-btn-color: var(--bs-gray-600);
    --bs-btn-border-color: var(--bs-gray-400);
    --bs-btn-hover-bg: var(--bs-gray-200);
    --bs-btn-hover-border-color: var(--bs-gray-500);
    font-size: 0.8rem;
    padding: 0.25rem 0.6rem;
}
.card-header .form-select-sm {
    font-size: 0.8rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.card-header .form-label.small {
    font-size: 0.8rem; /* Ensure label is small too */
    font-weight: 500;
}

/* Modal title icons */
.modal-title i.bi {
    color: var(--bs-primary);
}

/* Toast icon colors for different types */
.toast-header .bi-check-circle-fill { color: var(--bs-success) !important; }
.toast-header .bi-x-octagon-fill { color: var(--bs-danger) !important; }
.toast-header .bi-exclamation-triangle-fill { color: var(--bs-warning) !important; }
.toast-header .bi-info-circle-fill { color: var(--bs-info) !important; }

/* Ensure Bootstrap 5.3 subtle badge text has enough contrast on our modern table */
.modern-table .badge.bg-success-subtle { color: var(--bs-success-text-emphasis) !important; }
.modern-table .badge.bg-danger-subtle { color: var(--bs-danger-text-emphasis) !important; }
.modern-table .badge.bg-warning-subtle { color: var(--bs-warning-text-emphasis) !important; }
.modern-table .badge.bg-info-subtle { color: var(--bs-info-text-emphasis) !important; }
.modern-table .badge.bg-primary-subtle { color: var(--bs-primary-text-emphasis) !important; }
.modern-table .badge.bg-secondary-subtle { color: var(--bs-secondary-text-emphasis) !important; }


/* Fix for potential horizontal scrollbar issue if card-body has p-0 and table has margins or is too wide */
.card-body.p-0 {
    overflow-x: auto; /* Allow horizontal scrolling on card-body if table overflows */
}